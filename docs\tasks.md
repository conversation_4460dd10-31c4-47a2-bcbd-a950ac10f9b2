# WPF项目重构实现计划

## 实现任务

- [x] 1. 创建新的项目结构和基础架构



















  - 创建Clean Architecture分层项目结构
  - 设置项目引用关系和依赖方向
  - 配置NuGet包管理和版本控制
  - _需求: 1.1, 1.2, 5.1, 5.2_

- [-] 2. 实现领域层核心组件




- [x] 2.1 创建领域实体和值对象




  - 实现Node实体类，包含ID、类型、位置、属性管理
  - 实现Connection实体类，定义节点间连接关系
  - 实现Project聚合根，管理节点和连接集合
  - 创建相关值对象：NodeId、Position、NodeProperties
  - _需求: 1.1, 2.2_

- [ ] 2.2 实现领域服务和规约
  - 创建IWorkflowValidationService接口和实现
  - 实现节点连接验证规则
  - 创建工作流验证逻辑
  - 编写领域服务的单元测试
  - _需求: 1.1, 6.2, 7.1_

- [ ] 2.3 定义仓储接口
  - 创建IProjectRepository接口
  - 定义数据访问抽象方法
  - 创建领域事件接口定义
  - _需求: 1.3, 2.4_

- [ ] 3. 实现应用层服务
- [ ] 3.1 创建应用服务接口和实现
  - 实现INodeManagementService，处理节点CRUD操作
  - 实现IProjectService，处理项目保存加载
  - 实现IWorkflowExecutionService，处理工作流执行
  - 创建命令和查询模式的基础结构
  - _需求: 1.1, 1.3, 6.1, 6.2_

- [ ] 3.2 实现数据传输对象和映射
  - 创建NodeDto、ConnectionDto、ProjectDto类
  - 实现领域对象到DTO的映射逻辑
  - 创建AutoMapper配置文件
  - 编写映射逻辑的单元测试
  - _需求: 1.4, 7.1_

- [ ] 3.3 实现命令和查询处理器
  - 创建CreateNodeCommand和对应的处理器
  - 创建GetProjectNodesQuery和对应的处理器
  - 实现命令验证和错误处理
  - 编写处理器的单元测试
  - _需求: 1.1, 4.2, 7.1_

- [ ] 4. 实现基础设施层
- [ ] 4.1 实现数据持久化
  - 创建ProjectRepository具体实现
  - 实现文件系统存储机制
  - 创建数据序列化和反序列化逻辑
  - 编写数据访问层的集成测试
  - _需求: 1.3, 4.3, 7.2_

- [ ] 4.2 实现日志系统
  - 集成Microsoft.Extensions.Logging
  - 创建Logger<T>的具体实现
  - 配置日志输出格式和级别
  - 实现日志文件轮转机制
  - _需求: 4.1, 4.4_

- [ ] 4.3 实现配置管理
  - 创建IConfigurationService接口和实现
  - 实现JSON配置文件读写
  - 支持环境特定配置加载
  - 创建配置验证和默认值机制
  - _需求: 4.3, 4.4_

- [ ] 4.4 实现全局异常处理
  - 创建GlobalExceptionHandler类
  - 实现异常分类和用户友好消息转换
  - 集成到应用程序启动流程
  - 创建异常处理的单元测试
  - _需求: 4.2, 7.1_

- [ ] 5. 重构表示层架构
- [ ] 5.1 创建统一的ViewModel基类
  - 实现ViewModelBase抽象类
  - 包含INotifyPropertyChanged实现
  - 添加SetProperty辅助方法和命令支持
  - 实现IDisposable接口用于资源清理
  - _需求: 2.1, 3.1, 3.3_

- [ ] 5.2 重构MainWindow和MainWindowViewModel
  - 移除MainWindow.xaml.cs中的业务逻辑
  - 创建MainWindowViewModel继承ViewModelBase
  - 通过构造函数注入所需服务
  - 实现窗口初始化和布局管理逻辑
  - _需求: 3.1, 3.2, 3.3_

- [ ] 5.3 实现NodeEditor组件和ViewModel
  - 创建NodeEditorViewModel实现INodeEditorViewModel
  - 实现节点集合的ObservableCollection绑定
  - 创建节点操作命令：添加、删除、连接
  - 实现节点选择和属性更新逻辑
  - _需求: 3.3, 6.1, 6.3_

- [ ] 5.4 实现PropertyPanel组件
  - 创建PropertyPanelViewModel
  - 实现动态属性显示和编辑
  - 添加属性验证和实时更新功能
  - 创建属性编辑器的数据模板
  - _需求: 6.3, 8.3_

- [ ] 6. 实现依赖注入容器配置
- [ ] 6.1 配置服务注册
  - 创建ServiceConfiguration类
  - 注册所有应用服务、仓储和基础设施服务
  - 配置ViewModel的生命周期管理
  - 设置接口到实现的映射关系
  - _需求: 1.3, 2.4, 3.2_

- [ ] 6.2 集成到应用程序启动
  - 修改App.xaml.cs集成依赖注入容器
  - 实现服务定位器模式用于ViewModel创建
  - 配置应用程序生命周期事件处理
  - 添加启动时的服务验证逻辑
  - _需求: 2.4, 3.2_

- [ ] 7. 优化用户界面和交互
- [ ] 7.1 实现异步操作和进度反馈
  - 将文件IO操作改为异步实现
  - 添加IsBusy属性和进度指示器
  - 实现长时间操作的取消机制
  - 创建用户操作反馈的通知系统
  - _需求: 8.1, 8.2_

- [ ] 7.2 实现键盘快捷键支持
  - 创建快捷键命令绑定系统
  - 实现Ctrl+S保存、Ctrl+Z撤销等常用快捷键
  - 添加Delete键删除选中节点功能
  - 实现F5执行工作流快捷键
  - _需求: 8.4_

- [ ] 7.3 优化节点拖拽体验
  - 实现流畅的拖拽动画效果
  - 添加智能吸附和网格对齐功能
  - 创建拖拽预览和视觉反馈
  - 优化大量节点时的渲染性能
  - _需求: 6.1, 8.1_

- [ ] 8. 建立测试体系
- [ ] 8.1 创建单元测试项目结构
  - 为每个层创建对应的测试项目
  - 配置xUnit、Moq、FluentAssertions测试框架
  - 创建测试基类和通用测试工具
  - 设置测试数据和模拟对象
  - _需求: 7.1, 7.3_

- [ ] 8.2 编写领域层单元测试
  - 测试Node实体的业务规则和方法
  - 测试Project聚合根的操作逻辑
  - 测试领域服务的验证规则
  - 验证值对象的不变性和相等性
  - _需求: 7.1_

- [ ] 8.3 编写应用层单元测试
  - 测试应用服务的业务流程
  - 测试命令和查询处理器
  - 模拟仓储和外部依赖
  - 验证异常处理和错误场景
  - _需求: 7.1_

- [ ] 8.4 编写表示层单元测试
  - 测试ViewModel的属性绑定和命令
  - 测试用户交互逻辑
  - 模拟应用服务依赖
  - 验证UI状态变化和通知
  - _需求: 7.1_

- [ ] 9. 性能优化和最终集成
- [ ] 9.1 实现UI虚拟化和性能优化
  - 为大量节点场景实现UI虚拟化
  - 优化数据绑定性能，使用OneWay绑定
  - 实现节点模板和资源缓存
  - 添加内存使用监控和清理机制
  - _需求: 6.4, 8.1_

- [ ] 9.2 集成测试和端到端验证
  - 创建集成测试验证各层协作
  - 测试完整的用户操作流程
  - 验证配置管理和环境切换
  - 执行性能测试和压力测试
  - _需求: 7.2, 7.3_

- [ ] 9.3 代码重复清理和最终重构
  - 移除原有的重复ViewModelBase定义
  - 统一ModuleType枚举到Core层
  - 清理未使用的代码和依赖
  - 验证所有功能正常工作
  - _需求: 2.1, 2.2, 2.3_